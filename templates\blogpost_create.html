{% extends 'base.html' %}

{% block content %}
{# Main container with responsive grid #}
<div class="container">
  <div class="row justify-content-center">
    {# Responsive column layout - lg: 4 cols, md: 6 cols, sm: 12 cols #}
    <div class="col-md-6 col-md-6 col-sm-12">
      {# Form header #}
      <h2 class="account-heading">Create a New Blog Post</h2>

      {# Post creation form - Note: enctype required for file uploads #}
      <form method="post" enctype="multipart/form-data" id="blogPostForm">
        {% csrf_token %}

        {# Blog Title Field #}
        <div class="mb-3">
          {{ form.blog_title.label_tag }}
          {{ form.blog_title }}
          {% if form.blog_title.errors %}
          <ul>
            {% for error in form.blog_title.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
          <div id="blog-title-validation" class="validation-message"></div>
          <div class="character-count">
            <small class="text-muted">
              <span id="blog-title-count">0</span>/200 characters
            </small>
          </div>
        </div>

        {# Content Field #}
        <div class="mb-3">
          {{ form.content.label_tag }}
          {{ form.content }}
          {% if form.content.errors %}
          <ul>
            {% for error in form.content.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
          <div id="content-validation" class="validation-message"></div>
          <div class="character-count">
            <small class="text-muted">
              <span id="content-count">0</span>/10000 characters
            </small>
          </div>
        </div>

        {# Excerpt Field #}
        <div class="mb-3">
          {{ form.excerpt.label_tag }}
          {{ form.excerpt }}
          {% if form.excerpt.errors %}
          <ul>
            {% for error in form.excerpt.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
          <div id="excerpt-validation" class="validation-message"></div>
          <div class="character-count">
            <small class="text-muted">
              <span id="excerpt-count">0</span>/150 characters
            </small>
          </div>
        </div>

        {# Featured Image Field #}
        <div class="mb-3">
          {{ form.featured_image.label_tag }}
          {{ form.featured_image }}
          {% if form.featured_image.errors %}
          <ul>
            {% for error in form.featured_image.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Media Category Field #}
        <div class="mb-3">
          {{ form.media_category.label_tag }}
          {{ form.media_category }}
          {% if form.media_category.errors %}
          <ul>
            {% for error in form.media_category.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
        </div>

        {# Release Year Field #}
        <div class="mb-3">
          {{ form.release_year.label_tag }}
          {{ form.release_year }}
          {% if form.release_year.errors %}
          <ul>
            {% for error in form.release_year.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
          <div id="release-year-validation" class="validation-message"></div>
          <div class="field-help">
            <small class="text-muted">
              Enter a year between 1800 and {{ current_year }}
            </small>
          </div>
        </div>

        {# Media Link Field #}
        <div class="mb-3">
          {{ form.media_link.label_tag }}
          {{ form.media_link }}
          {% if form.media_link.errors %}
          <ul>
            {% for error in form.media_link.errors %}
            <li class="text-danger">{{ error }}</li>
            {% endfor %}
          </ul>
          {% endif %}
          <div id="media-link-validation" class="validation-message"></div>
          <div class="field-help">
            <small class="text-muted">
              Enter a valid URL (e.g., https://example.com)
            </small>
          </div>
        </div>

        <button type="submit" class="btn btn-primary">Create Post</button>
      </form>
    </div>
  </div>
</div>
{% endblock content %}

{% block scripts %}
{{ form.media }}

<style>
/* Validation styling */
.validation-message {
  margin-top: 5px;
  font-size: 0.875rem;
}

.validation-error {
  color: #dd1c1a;
  font-weight: 500;
}

.validation-success {
  color: #28a745;
  font-weight: 500;
}

.character-count {
  margin-top: 5px;
}

.character-count.valid {
  color: #28a745;
  font-weight: 500;
}

.character-count.warning {
  color: #ffc107;
  font-weight: 500;
}

.character-count.danger {
  color: #dd1c1a;
  font-weight: 500;
}

.field-help {
  margin-top: 5px;
}

.field-help.valid {
  color: #28a745;
  font-weight: 500;
}

.field-help.invalid {
  color: #dd1c1a;
  font-weight: 500;
}

.form-control.is-invalid {
  border-color: #dd1c1a;
  box-shadow: 0 0 0 0.2rem rgba(221, 28, 26, 0.25);
}

.form-control.is-valid {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get form elements
    const form = document.getElementById('blogPostForm');
    const blogTitle = document.getElementById('id_blog_title');
    const content = document.getElementById('id_content');
    const excerpt = document.getElementById('id_excerpt');
    const releaseYear = document.getElementById('id_release_year');
    const mediaLink = document.getElementById('id_media_link');

    // Get validation message containers
    const blogTitleValidation = document.getElementById('blog-title-validation');
    const contentValidation = document.getElementById('content-validation');
    const excerptValidation = document.getElementById('excerpt-validation');
    const releaseYearValidation = document.getElementById('release-year-validation');
    const mediaLinkValidation = document.getElementById('media-link-validation');

    // Get character count elements
    const blogTitleCount = document.getElementById('blog-title-count');
    const contentCount = document.getElementById('content-count');
    const excerptCount = document.getElementById('excerpt-count');

    // Current year for validation
    const currentYear = new Date().getFullYear();

    // Validation functions
    function showValidationMessage(element, message, isError = true) {
        element.innerHTML = `<span class="${isError ? 'validation-error' : 'validation-success'}">${message}</span>`;
    }

    function clearValidationMessage(element) {
        element.innerHTML = '';
    }

    function updateCharacterCount(input, countElement, maxLength, isFieldValid = true) {
        const currentLength = input.value.length;
        countElement.textContent = currentLength;

        const countContainer = countElement.parentElement.parentElement;
        countContainer.classList.remove('valid', 'warning', 'danger');

        if (!isFieldValid || currentLength >= maxLength) {
            countContainer.classList.add('danger');
        } else if (currentLength > maxLength * 0.9) {
            countContainer.classList.add('warning');
        } else {
            countContainer.classList.add('valid');
        }
    }

    function updateFieldHelp(helpElement, isValid) {
        helpElement.classList.remove('valid', 'invalid');
        helpElement.classList.add(isValid ? 'valid' : 'invalid');
    }

    function setFieldValidation(field, isValid) {
        field.classList.remove('is-valid', 'is-invalid');
        field.classList.add(isValid ? 'is-valid' : 'is-invalid');
    }

    // Blog Title Validation
    function validateBlogTitle() {
        const value = blogTitle.value.trim();
        let isValid = true;

        if (value.length === 0) {
            showValidationMessage(blogTitleValidation, 'Blog title is required');
            isValid = false;
        } else if (value.length < 5) {
            showValidationMessage(blogTitleValidation, 'Blog title must be at least 5 characters long');
            isValid = false;
        } else if (value.length > 200) {
            showValidationMessage(blogTitleValidation, 'Blog title cannot exceed 200 characters');
            isValid = false;
        } else {
            clearValidationMessage(blogTitleValidation);
            showValidationMessage(blogTitleValidation, '✓ Valid title', false);
        }

        setFieldValidation(blogTitle, isValid);
        updateCharacterCount(blogTitle, blogTitleCount, 200, isValid);
        return isValid;
    }

    // Content Validation
    function validateContent() {
        let value = content.value;

        // Handle Summernote content
        if (typeof $(content).summernote !== 'undefined') {
            value = $(content).summernote('code');
            // Strip HTML tags for character count
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = value;
            value = tempDiv.textContent || tempDiv.innerText || '';
        }

        value = value.trim();
        let isValid = true;

        if (value.length === 0) {
            showValidationMessage(contentValidation, 'Content is required');
            isValid = false;
        } else if (value.length < 50) {
            showValidationMessage(contentValidation, 'Content must be at least 50 characters long');
            isValid = false;
        } else if (value.length > 10000) {
            showValidationMessage(contentValidation, 'Content cannot exceed 10,000 characters');
            isValid = false;
        } else {
            clearValidationMessage(contentValidation);
            showValidationMessage(contentValidation, '✓ Valid content', false);
        }

        setFieldValidation(content, isValid);
        if (contentCount) {
            contentCount.textContent = value.length;
            // Update character count color based on validity
            const countContainer = contentCount.parentElement.parentElement;
            countContainer.classList.remove('valid', 'warning', 'danger');
            if (!isValid || value.length >= 10000) {
                countContainer.classList.add('danger');
            } else if (value.length > 9000) {
                countContainer.classList.add('warning');
            } else {
                countContainer.classList.add('valid');
            }
        }
        return isValid;
    }

    // Excerpt Validation
    function validateExcerpt() {
        const value = excerpt.value.trim();
        let isValid = true;

        if (value.length === 0) {
            showValidationMessage(excerptValidation, 'Excerpt is required');
            isValid = false;
        } else if (value.length < 10) {
            showValidationMessage(excerptValidation, 'Excerpt must be at least 10 characters long');
            isValid = false;
        } else if (value.length > 150) {
            showValidationMessage(excerptValidation, 'Excerpt cannot exceed 150 characters');
            isValid = false;
        } else {
            clearValidationMessage(excerptValidation);
            showValidationMessage(excerptValidation, '✓ Valid excerpt', false);
        }

        setFieldValidation(excerpt, isValid);
        updateCharacterCount(excerpt, excerptCount, 150, isValid);
        return isValid;
    }

    // Release Year Validation
    function validateReleaseYear() {
        const value = releaseYear.value.trim();
        let isValid = true;

        // Get the field help element
        const fieldHelp = releaseYear.parentElement.querySelector('.field-help');

        if (value === '') {
            showValidationMessage(releaseYearValidation, 'Release year is required');
            isValid = false;
        } else if (!/^\d{4}$/.test(value)) {
            showValidationMessage(releaseYearValidation, 'Please enter a valid 4-digit year');
            isValid = false;
        } else {
            const year = parseInt(value);
            if (year < 1800) {
                showValidationMessage(releaseYearValidation, 'Year cannot be before 1800');
                isValid = false;
            } else if (year > currentYear) {
                showValidationMessage(releaseYearValidation, `Year cannot be after ${currentYear}`);
                isValid = false;
            } else {
                clearValidationMessage(releaseYearValidation);
                showValidationMessage(releaseYearValidation, '✓ Valid year', false);
            }
        }

        setFieldValidation(releaseYear, isValid);

        // Update field help color
        if (fieldHelp) {
            updateFieldHelp(fieldHelp, isValid);
        }

        return isValid;
    }

    // Media Link Validation
    function validateMediaLink() {
        const value = mediaLink.value.trim();
        let isValid = true;

        // Get the field help element
        const fieldHelp = mediaLink.parentElement.querySelector('.field-help');

        if (value === '' || value === 'http://www.') {
            showValidationMessage(mediaLinkValidation, 'Media link is required');
            isValid = false;
        } else {
            // URL validation regex
            const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
            if (!urlPattern.test(value)) {
                showValidationMessage(mediaLinkValidation, 'Please enter a valid URL');
                isValid = false;
            } else if (!value.startsWith('http://') && !value.startsWith('https://')) {
                showValidationMessage(mediaLinkValidation, 'URL should start with http:// or https://');
                isValid = false;
            } else {
                clearValidationMessage(mediaLinkValidation);
                showValidationMessage(mediaLinkValidation, '✓ Valid URL', false);
            }
        }

        setFieldValidation(mediaLink, isValid);

        // Update field help color
        if (fieldHelp) {
            updateFieldHelp(fieldHelp, isValid);
        }

        return isValid;
    }

    // Event listeners for real-time validation
    if (blogTitle) {
        blogTitle.addEventListener('input', validateBlogTitle);
        blogTitle.addEventListener('blur', validateBlogTitle);
    }

    if (content) {
        content.addEventListener('input', validateContent);
        content.addEventListener('blur', validateContent);

        // Handle Summernote events if present
        if (typeof $ !== 'undefined' && typeof $(content).summernote !== 'undefined') {
            $(content).on('summernote.change', validateContent);
        }
    }

    if (excerpt) {
        excerpt.addEventListener('input', validateExcerpt);
        excerpt.addEventListener('blur', validateExcerpt);
    }

    if (releaseYear) {
        releaseYear.addEventListener('input', validateReleaseYear);
        releaseYear.addEventListener('blur', validateReleaseYear);
    }

    if (mediaLink) {
        mediaLink.addEventListener('input', validateMediaLink);
        mediaLink.addEventListener('blur', validateMediaLink);
    }

    // Form submission validation
    form.addEventListener('submit', function(e) {
        let isFormValid = true;

        // Validate all fields
        if (blogTitle && !validateBlogTitle()) isFormValid = false;
        if (content && !validateContent()) isFormValid = false;
        if (excerpt && !validateExcerpt()) isFormValid = false;
        if (releaseYear && !validateReleaseYear()) isFormValid = false;
        if (mediaLink && !validateMediaLink()) isFormValid = false;

        if (!isFormValid) {
            e.preventDefault();

            // Scroll to first error
            const firstError = document.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                firstError.focus();
            }

            // Show general error message
            alert('Please correct the errors in the form before submitting.');
        }
    });

    // Initialize character counts and field help colors
    if (blogTitle && blogTitleCount) updateCharacterCount(blogTitle, blogTitleCount, 200, true);
    if (excerpt && excerptCount) updateCharacterCount(excerpt, excerptCount, 150, true);

    // Initialize field help colors
    const releaseYearHelp = releaseYear ? releaseYear.parentElement.querySelector('.field-help') : null;
    const mediaLinkHelp = mediaLink ? mediaLink.parentElement.querySelector('.field-help') : null;

    if (releaseYearHelp) updateFieldHelp(releaseYearHelp, false); // Start as invalid (red)
    if (mediaLinkHelp) updateFieldHelp(mediaLinkHelp, false); // Start as invalid (red)
});
</script>
{% endblock scripts %}
