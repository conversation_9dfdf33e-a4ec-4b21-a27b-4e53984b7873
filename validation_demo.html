<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blog Post Validation Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.1/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        /* Validation styling */
        .validation-message {
            margin-top: 5px;
            font-size: 0.875rem;
        }

        .validation-error {
            color: #dd1c1a;
            font-weight: 500;
        }

        .validation-success {
            color: #28a745;
            font-weight: 500;
        }

        .character-count {
            margin-top: 5px;
        }

        .character-count.warning {
            color: #ffc107;
        }

        .character-count.danger {
            color: #dd1c1a;
        }

        .field-help {
            margin-top: 5px;
        }

        .form-control.is-invalid {
            border-color: #dd1c1a;
            box-shadow: 0 0 0 0.2rem rgba(221, 28, 26, 0.25);
        }

        .form-control.is-valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .demo-container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
        }

        .demo-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }

        .validation-features {
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🚀 Blog Post Validation Demo</h1>
            <p class="mb-0">Real-time input validation for Create New Blog Post form</p>
        </div>

        <div class="validation-features">
            <h3>✨ Validation Features</h3>
            <ul>
                <li><strong>Blog Title:</strong> Required, 5-200 characters, real-time character count</li>
                <li><strong>Content:</strong> Required, 50-10,000 characters, HTML content support</li>
                <li><strong>Excerpt:</strong> Required, 10-150 characters, character count with warnings</li>
                <li><strong>Release Year:</strong> Required, 4-digit year between 1800 and current year</li>
                <li><strong>Media Link:</strong> Required, valid URL format with protocol validation</li>
            </ul>
        </div>

        <form id="blogPostForm" class="needs-validation" novalidate>
            <!-- Blog Title Field -->
            <div class="mb-3">
                <label for="id_blog_title" class="form-label">Blog Title</label>
                <input type="text" class="form-control" id="id_blog_title" name="blog_title" 
                       placeholder="Enter a title for your post... (max length 200 characters)" maxlength="200">
                <div id="blog-title-validation" class="validation-message"></div>
                <div class="character-count">
                    <small class="text-muted">
                        <span id="blog-title-count">0</span>/200 characters
                    </small>
                </div>
            </div>

            <!-- Content Field -->
            <div class="mb-3">
                <label for="id_content" class="form-label">Content</label>
                <textarea class="form-control" id="id_content" name="content" rows="6"
                          placeholder="Write your blog content here... (max length 10000 characters)" maxlength="10000"></textarea>
                <div id="content-validation" class="validation-message"></div>
                <div class="character-count">
                    <small class="text-muted">
                        <span id="content-count">0</span>/10000 characters
                    </small>
                </div>
            </div>

            <!-- Excerpt Field -->
            <div class="mb-3">
                <label for="id_excerpt" class="form-label">Excerpt</label>
                <textarea class="form-control" id="id_excerpt" name="excerpt" rows="2"
                          placeholder="Write a short excerpt... (max length 150 characters)" maxlength="150"></textarea>
                <div id="excerpt-validation" class="validation-message"></div>
                <div class="character-count">
                    <small class="text-muted">
                        <span id="excerpt-count">0</span>/150 characters
                    </small>
                </div>
            </div>

            <!-- Release Year Field -->
            <div class="mb-3">
                <label for="id_release_year" class="form-label">Release Year</label>
                <input type="number" class="form-control" id="id_release_year" name="release_year"
                       min="1800" max="2025" placeholder="Format YYYY">
                <div id="release-year-validation" class="validation-message"></div>
                <div class="field-help">
                    <small class="text-muted">
                        Enter a year between 1800 and 2025
                    </small>
                </div>
            </div>

            <!-- Media Link Field -->
            <div class="mb-3">
                <label for="id_media_link" class="form-label">Media Link / Reference</label>
                <input type="url" class="form-control" id="id_media_link" name="media_link" 
                       value="http://www." placeholder="Enter a valid URL">
                <div id="media-link-validation" class="validation-message"></div>
                <div class="field-help">
                    <small class="text-muted">
                        Enter a valid URL (e.g., https://example.com)
                    </small>
                </div>
            </div>

            <button type="submit" class="btn btn-primary btn-lg">🎯 Test Validation</button>
            <button type="button" class="btn btn-secondary btn-lg ms-2" onclick="fillSampleData()">📝 Fill Sample Data</button>
        </form>

        <div class="mt-4 p-3 bg-light rounded">
            <h5>🧪 Try These Tests:</h5>
            <ol>
                <li>Leave fields empty and try to submit</li>
                <li>Enter text shorter than minimum requirements</li>
                <li>Enter text longer than maximum limits</li>
                <li>Enter invalid year (like 1799 or 2026)</li>
                <li>Enter invalid URL (without http/https)</li>
                <li>Watch character counts change color as you approach limits</li>
            </ol>
        </div>
    </div>

    <script>
        // Sample data function
        function fillSampleData() {
            document.getElementById('id_blog_title').value = 'Amazing Movie Review: The Best Film of 2024';
            document.getElementById('id_content').value = 'This is an incredible movie that showcases amazing cinematography and storytelling. The plot is engaging from start to finish, with excellent character development and stunning visual effects. I highly recommend this film to anyone who enjoys quality cinema.';
            document.getElementById('id_excerpt').value = 'An incredible movie with amazing cinematography and engaging plot that I highly recommend to all cinema lovers.';
            document.getElementById('id_release_year').value = '2024';
            document.getElementById('id_media_link').value = 'https://www.imdb.com/title/example';
            
            // Trigger validation for all fields
            validateBlogTitle();
            validateContent();
            validateExcerpt();
            validateReleaseYear();
            validateMediaLink();
        }

        // Validation code (same as in the actual template)
        document.addEventListener('DOMContentLoaded', function() {
            // Get form elements
            const form = document.getElementById('blogPostForm');
            const blogTitle = document.getElementById('id_blog_title');
            const content = document.getElementById('id_content');
            const excerpt = document.getElementById('id_excerpt');
            const releaseYear = document.getElementById('id_release_year');
            const mediaLink = document.getElementById('id_media_link');
            
            // Get validation message containers
            const blogTitleValidation = document.getElementById('blog-title-validation');
            const contentValidation = document.getElementById('content-validation');
            const excerptValidation = document.getElementById('excerpt-validation');
            const releaseYearValidation = document.getElementById('release-year-validation');
            const mediaLinkValidation = document.getElementById('media-link-validation');
            
            // Get character count elements
            const blogTitleCount = document.getElementById('blog-title-count');
            const contentCount = document.getElementById('content-count');
            const excerptCount = document.getElementById('excerpt-count');
            
            // Current year for validation
            const currentYear = new Date().getFullYear();
            
            // Validation functions
            function showValidationMessage(element, message, isError = true) {
                element.innerHTML = `<span class="${isError ? 'validation-error' : 'validation-success'}">${message}</span>`;
            }
            
            function clearValidationMessage(element) {
                element.innerHTML = '';
            }
            
            function updateCharacterCount(input, countElement, maxLength) {
                const currentLength = input.value.length;
                countElement.textContent = currentLength;
                
                const countContainer = countElement.parentElement.parentElement;
                countContainer.classList.remove('warning', 'danger');
                
                if (currentLength > maxLength * 0.9) {
                    countContainer.classList.add('warning');
                }
                if (currentLength >= maxLength) {
                    countContainer.classList.add('danger');
                }
            }
            
            function setFieldValidation(field, isValid) {
                field.classList.remove('is-valid', 'is-invalid');
                field.classList.add(isValid ? 'is-valid' : 'is-invalid');
            }
            
            // Blog Title Validation
            window.validateBlogTitle = function() {
                const value = blogTitle.value.trim();
                let isValid = true;
                
                if (value.length === 0) {
                    showValidationMessage(blogTitleValidation, 'Blog title is required');
                    isValid = false;
                } else if (value.length < 5) {
                    showValidationMessage(blogTitleValidation, 'Blog title must be at least 5 characters long');
                    isValid = false;
                } else if (value.length > 200) {
                    showValidationMessage(blogTitleValidation, 'Blog title cannot exceed 200 characters');
                    isValid = false;
                } else {
                    clearValidationMessage(blogTitleValidation);
                    showValidationMessage(blogTitleValidation, '✓ Valid title', false);
                }
                
                setFieldValidation(blogTitle, isValid);
                updateCharacterCount(blogTitle, blogTitleCount, 200);
                return isValid;
            }
            
            // Content Validation
            window.validateContent = function() {
                const value = content.value.trim();
                let isValid = true;
                
                if (value.length === 0) {
                    showValidationMessage(contentValidation, 'Content is required');
                    isValid = false;
                } else if (value.length < 50) {
                    showValidationMessage(contentValidation, 'Content must be at least 50 characters long');
                    isValid = false;
                } else if (value.length > 10000) {
                    showValidationMessage(contentValidation, 'Content cannot exceed 10,000 characters');
                    isValid = false;
                } else {
                    clearValidationMessage(contentValidation);
                    showValidationMessage(contentValidation, '✓ Valid content', false);
                }
                
                setFieldValidation(content, isValid);
                if (contentCount) {
                    contentCount.textContent = value.length;
                }
                return isValid;
            }
            
            // Excerpt Validation
            window.validateExcerpt = function() {
                const value = excerpt.value.trim();
                let isValid = true;
                
                if (value.length === 0) {
                    showValidationMessage(excerptValidation, 'Excerpt is required');
                    isValid = false;
                } else if (value.length < 10) {
                    showValidationMessage(excerptValidation, 'Excerpt must be at least 10 characters long');
                    isValid = false;
                } else if (value.length > 150) {
                    showValidationMessage(excerptValidation, 'Excerpt cannot exceed 150 characters');
                    isValid = false;
                } else {
                    clearValidationMessage(excerptValidation);
                    showValidationMessage(excerptValidation, '✓ Valid excerpt', false);
                }
                
                setFieldValidation(excerpt, isValid);
                updateCharacterCount(excerpt, excerptCount, 150);
                return isValid;
            }
            
            // Release Year Validation
            window.validateReleaseYear = function() {
                const value = releaseYear.value.trim();
                let isValid = true;
                
                if (value === '') {
                    showValidationMessage(releaseYearValidation, 'Release year is required');
                    isValid = false;
                } else if (!/^\d{4}$/.test(value)) {
                    showValidationMessage(releaseYearValidation, 'Please enter a valid 4-digit year');
                    isValid = false;
                } else {
                    const year = parseInt(value);
                    if (year < 1800) {
                        showValidationMessage(releaseYearValidation, 'Year cannot be before 1800');
                        isValid = false;
                    } else if (year > currentYear) {
                        showValidationMessage(releaseYearValidation, `Year cannot be after ${currentYear}`);
                        isValid = false;
                    } else {
                        clearValidationMessage(releaseYearValidation);
                        showValidationMessage(releaseYearValidation, '✓ Valid year', false);
                    }
                }
                
                setFieldValidation(releaseYear, isValid);
                return isValid;
            }
            
            // Media Link Validation
            window.validateMediaLink = function() {
                const value = mediaLink.value.trim();
                let isValid = true;
                
                if (value === '' || value === 'http://www.') {
                    showValidationMessage(mediaLinkValidation, 'Media link is required');
                    isValid = false;
                } else {
                    // URL validation regex
                    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
                    if (!urlPattern.test(value)) {
                        showValidationMessage(mediaLinkValidation, 'Please enter a valid URL');
                        isValid = false;
                    } else if (!value.startsWith('http://') && !value.startsWith('https://')) {
                        showValidationMessage(mediaLinkValidation, 'URL should start with http:// or https://');
                        isValid = false;
                    } else {
                        clearValidationMessage(mediaLinkValidation);
                        showValidationMessage(mediaLinkValidation, '✓ Valid URL', false);
                    }
                }
                
                setFieldValidation(mediaLink, isValid);
                return isValid;
            }
            
            // Event listeners for real-time validation
            if (blogTitle) {
                blogTitle.addEventListener('input', validateBlogTitle);
                blogTitle.addEventListener('blur', validateBlogTitle);
            }
            
            if (content) {
                content.addEventListener('input', validateContent);
                content.addEventListener('blur', validateContent);
            }
            
            if (excerpt) {
                excerpt.addEventListener('input', validateExcerpt);
                excerpt.addEventListener('blur', validateExcerpt);
            }
            
            if (releaseYear) {
                releaseYear.addEventListener('input', validateReleaseYear);
                releaseYear.addEventListener('blur', validateReleaseYear);
            }
            
            if (mediaLink) {
                mediaLink.addEventListener('input', validateMediaLink);
                mediaLink.addEventListener('blur', validateMediaLink);
            }
            
            // Form submission validation
            form.addEventListener('submit', function(e) {
                e.preventDefault(); // Prevent actual submission for demo
                
                let isFormValid = true;
                
                // Validate all fields
                if (blogTitle && !validateBlogTitle()) isFormValid = false;
                if (content && !validateContent()) isFormValid = false;
                if (excerpt && !validateExcerpt()) isFormValid = false;
                if (releaseYear && !validateReleaseYear()) isFormValid = false;
                if (mediaLink && !validateMediaLink()) isFormValid = false;
                
                if (!isFormValid) {
                    // Scroll to first error
                    const firstError = document.querySelector('.is-invalid');
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        firstError.focus();
                    }
                    
                    // Show general error message
                    alert('❌ Please correct the errors in the form before submitting.');
                } else {
                    alert('✅ All validation passed! Form is ready to submit.');
                }
            });
            
            // Initialize character counts
            if (blogTitle && blogTitleCount) updateCharacterCount(blogTitle, blogTitleCount, 200);
            if (excerpt && excerptCount) updateCharacterCount(excerpt, excerptCount, 150);
        });
    </script>
</body>
</html>
